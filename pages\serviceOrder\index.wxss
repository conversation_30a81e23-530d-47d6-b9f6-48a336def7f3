.container {
  background-color: #f1f1f1;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

.order-tabs {
  display: flex;
  background-color: #fff;
  height: 120rpx;
  align-items: center;
}

.tab-item {
  flex: 1;
  text-align: center;
  color: #666;
  font-size: 28rpx;
  position: relative;
  line-height: 70rpx;
}

.tab-item.active {
  color: #333;
  font-weight: bold;
  font-size: 30rpx;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 30rpx;
  height: 8rpx;
  border-radius: 8rpx;
  background-color: #FF4391;
}

.order-list {
  height: calc(100vh - 200rpx);
}

.order-item {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 10rpx;
  position: relative;
  overflow: hidden;
}

/* 可点击的订单主体区域 */
.order-clickable-area {
  padding: 20rpx;
  padding-bottom: 10rpx;
  position: relative;
  transition: background-color 0.2s ease;
}

.order-clickable-area:active {
  background-color: #f8f8f8;
}

/* 添加点击提示 */
.order-clickable-area::after {
  content: '';
  position: absolute;
  top: 50%;
  right: 20rpx;
  transform: translateY(-50%);
  width: 12rpx;
  height: 12rpx;
  border-top: 2rpx solid #ccc;
  border-right: 2rpx solid #ccc;
  transform: translateY(-50%) rotate(45deg);
  opacity: 0.6;
}

/* 操作按钮区域 */
.order-actions-area {
  padding: 10rpx 20rpx 20rpx 20rpx;
  border-top: 1rpx solid #f0f0f0;
  background-color: #fff;
}

.order-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
  padding-bottom: 10rpx;
}

/* 订单详情信息区域 */
.order-details {
  margin-top: 10rpx;
}

.order-number {
  font-size: 24rpx;
  color: #999;
}

.order-status {
  font-size: 28rpx;
  color: #FF4391;
}

/* 头部追加服务提醒样式 */
.additional-reminder-header {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #FF4391, #FF6B9D);
  border-radius: 12rpx;
  padding: 8rpx 16rpx;
  position: relative;
}

.additional-reminder-header::before {
  content: '';
  position: absolute;
  left: 8rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 8rpx;
  background-color: #fff;
  border-radius: 50%;
  animation: pulse 1.5s infinite;
}

.reminder-text-header {
  font-size: 24rpx;
  color: #fff;
  font-weight: 500;
  margin-left: 18rpx;
}

/* 内联订单号样式 */
.order-number-inline {
  font-size: 24rpx;
  color: #999;
}

.order-content {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.product-image {
  width: 150rpx;
  height: 150rpx;
  margin-right: 20rpx;
  border-radius: 10rpx;
}

.product-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.product-service {
  background: rgba(255, 67, 145, 0.1);
  border-radius: 8px;
  padding: 8rpx 12rpx;
  width: 70%;
  line-height: 50rpx;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.product-quantity {
  font-size: 24rpx;
  color: #999;
}

.paid-money {
  text-align: right;
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

/* 价格区域样式 */
.price-section {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.additional-price-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  min-width: 160rpx;
}

.price-row {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.price-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 8rpx;
}

.price-breakdown {
  display: flex;
  gap: 8rpx;
  margin-bottom: 8rpx;
  flex-wrap: wrap;
  justify-content: flex-end;
}

.breakdown-item {
  font-size: 20rpx;
  color: #999;
  background-color: #f5f5f5;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  white-space: nowrap;
}

/* 追加服务提醒样式 */
.additional-reminder {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #FF4391, #FF6B9D);
  border-radius: 10rpx;
  padding: 6rpx 12rpx;
  margin-top: 6rpx;
  position: relative;
}

.additional-reminder::before {
  content: '';
  position: absolute;
  left: 6rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 8rpx;
  background-color: #fff;
  border-radius: 50%;
  animation: pulse 1.5s infinite;
}

.reminder-text {
  font-size: 20rpx;
  color: #fff;
  font-weight: 500;
  margin-left: 16rpx;
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: translateY(-50%) scale(1);
  }
  50% {
    opacity: 0.6;
    transform: translateY(-50%) scale(1.2);
  }
  100% {
    opacity: 1;
    transform: translateY(-50%) scale(1);
  }
}


.magin-bottom{
  margin-bottom: 20rpx;
}

.more-btn {
  visibility: hidden;
  margin-right: 20rpx;
  color: rgba(255, 67, 145, 1);
  font-size: 24rpx;
  position: relative;
}

.more-actions-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 260rpx;
  background-color: #fff;
  border-radius: 10rpx;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 100;
  overflow: hidden;
}

.dropdown-item {
  padding: 20rpx;
  text-align: center;
  font-size: 24rpx;
  color: #333;
  border-bottom: 1rpx solid #eee;
  background-color: #fff;
  transition: background-color 0.3s;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:hover {
  background-color: #f4f4f4;
}

.order-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.action-btn {
  padding: 10rpx 20rpx;
  border-radius: 5rpx;
  font-size: 24rpx;
  background: rgba(238, 238, 238, 1);
  color: rgba(51, 51, 51, 1);
  border-radius: 40rpx;
  margin-left: 20rpx;
}

.action-btn:nth-last-child(1) {
  background: rgba(47, 131, 255, 0.2);
  color: rgba(47, 131, 255, 1);
  border-radius: 40rpx;
}

.blue-btn {
  background: rgba(47, 131, 255, 0.2) !important;
  color: rgba(47, 131, 255, 1) !important;
}

.gray-btn {
  background: rgba(238, 238, 238, 1) !important;
  color: rgba(102, 102, 102, 1) !important;
}

.review-btn {
  background: rgba(255, 165, 0, 1) !important;
  color: white !important;
}

.empty-list {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-top: 200rpx;
}

.empty-image {
  width: 300rpx;
  height: 300rpx;
}

.empty-text {
  margin-top: 20rpx;
  color: #999;
}

/* 模态框底部样式 */
.modal-footer {
  display: flex;
  justify-content: center;
  padding: 20rpx 0;
  border-top: 1rpx solid #eee;
  margin-top: 20rpx;
}

.agreement-link {
  color: #2F83FF;
  font-size: 28rpx;
  text-decoration: underline;
  padding: 10rpx 20rpx;
}

/* 用户备注预览样式 */
.user-remark-preview {
  color: #666;
  font-size: 24rpx;
  line-height: 1.4;
  max-width: 100%;
  word-break: break-all;
}